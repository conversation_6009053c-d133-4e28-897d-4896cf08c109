import { CalculationGroupPlain, CalculationGroupValueType, CalculationType } from "../../../server/models/calculationGroup";
import { ObjectId } from "mongodb";
import { UniversalTrackerConnectionPlain } from '../../../server/models/universalTrackerConnection';
import { NumberScale, SupportedMeasureUnits } from "../../../server/service/units/unitTypes";


const calculationOne: CalculationGroupPlain['calculations'][number] = {
  _id: new ObjectId(),
  name: 'Calculation One',
  type: CalculationType.Direct,
  direct: 'a',
  variables: {
    a: {
      code: 'calculation-one',
      valueListCode: 'calculation-one',
      integrationCode: 'calculation',
    },
  },
};

export const calculationGroupOne: CalculationGroupPlain = {
  _id: new ObjectId(),
  code: 'calculation-group-one',
  name: 'Calculation Group One',
  valueType: CalculationGroupValueType.Numeric,
  calculations: [calculationOne],
  created: new Date('2025-06-02T15:00:00.000Z'),
  oktaId: '',
};


export const calculationConnectionOne: UniversalTrackerConnectionPlain = {
  _id: new ObjectId(),
  code: 'calculation-connection-one',
  utrCode: 'gri/2020/305-1/a',
  calculations: [{ calculationGroupId: calculationGroupOne._id }],
};

export const greenlyTotalScope1: CalculationGroupPlain = {
  _id: new ObjectId('665800000000000000000001'),
  code: 'greenly-total-scope-1',
  name: 'Greenly Total Scope 1',
  valueType: CalculationGroupValueType.Numeric,
  numberScale: NumberScale.Single,
  unitType: SupportedMeasureUnits.mass,
  calculations: [
    {
      _id: new ObjectId('665800000000000000000002'),
      name: 'Greenly Total Scope 1',
      type: CalculationType.Direct,
      direct: 'a',
      variables: {
        a: {
          code: 'greenly-total-scope-1',
          valueListCode: 'scope_1_total',
          integrationCode: 'greenly',
        },
      },
    },
  ],
  created: new Date('2025-06-02T15:00:00.000Z'),
  oktaId: '',
};
