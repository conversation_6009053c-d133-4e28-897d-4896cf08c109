/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import {
  DataTransform,
  MinTransformData,
  TransformOptions,
  UtrData,
} from '../../../../server/service/survey/transfer/DataTransform';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import {
  ColumnType,
  TableColumn,
  UtrValueType,
  ValueValidation,
  ValueValidationType,
} from '../../../../server/models/public/universalTrackerType';
import {
  countryListOne,
  griValueListTestOne,
  valueListTestTable,
  yesNoList,
} from '../../../fixtures/valueListFixtures';
import { ValueList } from '../../../../server/models/public/valueList';
import { generatedUUID } from '../../../../server/service/crypto/token';
import { createSandbox } from 'sinon';
import { ValueListRepository } from '../../../../server/repository/ValueListRepository';
import { createUtrFromCode } from '../../../fixtures/universalTrackerFixtures';
import { createUtrv } from '../../../fixtures/compositeUTRVFixtures';
import { createCombinedFromCode } from '../../../factories/universalTrackerValue';
import { NotApplicableTypes } from '../../../../server/models/universalTrackerValue';
import { TableData } from "../../../../server/models/public/universalTrackerValueType";
import { SupportedMeasureUnits } from '../../../../server/service/units/unitTypes';
import { getNumericCellFormat } from '../../../fixtures/universalTrackerValueFixtures';

const TYPE_CODE_VALUE = 'typeCode/101';
const standardCodeGri = 'gri';
const name = 'test name';

interface ExpectedResult {
  data: Record<string, unknown>,
  dataInput: Record<string, unknown>,
  note: string[],
  value?: number;
  inputValue?: number;
}

interface ExpectedTableResult {
  table: { code: string, value: unknown}[],
  inputTable: { code: string, value: unknown, unit: string | undefined }[],
  note: string[]
}

type RowData = {
  inputValue: string | number | undefined,
  comment?: string | undefined,
  inputText?: string,
  expectedValue?: string | number,
  expectedInputValue?: string | number,
  inputUnit?: string
}

type ColumnTestData = TableColumn & { value: unknown, comment?: string, importUnit?: string };
describe('DataTransform', function () {

  const sandbox = createSandbox();

  before(() => {
    sandbox.stub(ValueListRepository, 'findByIds').resolves([
      countryListOne,
      griValueListTestOne,
      yesNoList,
    ])
  })
  after(() => sandbox.restore());

  const dt = new DataTransform();
  const baseOptions: TransformOptions = {
    mapping: {},
    utrs: [],
  };

  const createValueValidation = (list: ValueList): ValueValidation => ({
    valueList: {
      list: list.options,
      type: ValueValidationType.List,
      listId: list._id,
      custom: undefined,
    },
  });

  const createTableValidation = (cols: TableColumn[], maxRows?: number): ValueValidation => ({
    table: {
      validation: maxRows ? { maxRows } : undefined,
      columns: [...cols],
    },
  });


  const createColumn = (col: Partial<ColumnTestData>): ColumnTestData => {
    const code = col.code ?? 'col-' + generatedUUID();
    return {
      name: code,
      type: ColumnType.Text,
      code: code,
      value: undefined,
      ...col,
    }
  }


  const createUtrs = (dataProvider: Record<string, any>[]): UtrData[] => dataProvider.map(u => {
    return ({
      _id: new ObjectId(),
      code: u.QuestionCode as string,
      typeCode: u.TypeCode,
      valueLabel: u.Question,
      valueType: u.utrValueType,
      valueValidation: u.utrValueValidation,
      unitType: u.utrUnitType,
      unit: u.utrUnit,
      type: u.type,
      name: u.name,
    });
  });

  type DataTableProvider = ReturnType<typeof createRow>;

  const createRow = (
    columns: ColumnTestData[],
    code?: string,
    maxRows?: number,
  ) => {
    const questionCode = code ?? `${UtrValueType.Table}-${generatedUUID()}`;
    return {
      QuestionCode: questionCode,
      Question: questionCode,
      utrValueType: UtrValueType.Table,
      utrValueValidation: createTableValidation(columns, maxRows),
      rows: columns.map((o) => ({
        OptionCode: o.code,
        Value: o.value,
        Comment: o.comment,
        Unit: o.importUnit,
      })),
    };
  };

  const convertDataProviderToMap = (dataProvider: DataTableProvider[]) => {
    const map = new Map<string, DataTableProvider>()
    dataProvider.forEach((utrRow) => {
      const mapEntry = map.get(utrRow.QuestionCode);
      if (mapEntry) {
        mapEntry.rows = mapEntry.rows.concat(utrRow.rows);
      } else {
        map.set(utrRow.QuestionCode, utrRow);
      }
    })
    return map;
  };

  const reduceToRowData = (dataProvider: DataTableProvider[]) => dataProvider.reduce((acc, utrData) => {
    const { rows, Question, QuestionCode } = utrData;
    rows.forEach(r => {
      acc.push({ Question, QuestionCode, ...r })
    })
    return acc;
  }, <unknown[]>[]);

  const createValueListRow = (type: UtrValueType, valueList: ValueList, rows: RowData[], utrProps?: { typeCode?: string, unit?: string, unitType?: SupportedMeasureUnits }, skip?: boolean) => {
        const questionCode = `${type}-${valueList.code}-${Math.random()}`;
        return {
          QuestionCode: questionCode,
          Question: questionCode,
          TypeCode: utrProps?.typeCode,
          utrValueType: type,
          utrValueValidation: createValueValidation(valueList),
          utrUnit: utrProps?.unit,
          utrUnitType: utrProps?.unitType,
          skip,
          rows: valueList.options.slice(0, rows.length).map((o, i) => {
            return ({
            OptionCode: o.code,
            Value: rows[i]?.inputValue,
            Comment: rows[i]?.comment,
            Unit: rows[i]?.inputText,
            ExpectedValue: rows[i]?.expectedValue,
            ExpectedInputValue: rows[i]?.expectedInputValue,
            InputUnit: rows[i]?.inputUnit,
          })}),
        }
      }

  describe('transform fn', function () {

    it('should work with empty array', async () => {
      const result = await dt.transform([], baseOptions)
      expect(result).eqls([]);
    });

    it('should work with number/percentage', async () => {
      const dataWithoutTypeCode: MinTransformData[] = [
        {
          utrCode: 'numba',
          utrValueType: UtrValueType.Number,
          value: 2,
        },
        {
          utrCode: 'numba1',
          utrValueType: UtrValueType.Percentage,
          value: 5,
        },
        {
          utrCode: 'numbaNA',
          utrValueType: UtrValueType.Percentage,
          value: undefined,
          notApplicableType: NotApplicableTypes.NA
        },
        {
          utrCode: 'numbaPercentage',
          utrValueType: UtrValueType.Percentage,
          value: '',
          skipped: true,
        },
        {
          utrCode: 'numbaPercentage',
          utrValueType: UtrValueType.Percentage,
          value: undefined,
          skipped: true,
        },
        {
          utrCode: 'numbaAA',
          utrValueType: UtrValueType.Number,
          value: undefined,
          Unit: 'm3',
          utrUnit: 'km3',
          utrUnitType: SupportedMeasureUnits.volume,
          skipped: true,
        },
        {
          utrCode: 'numbaAA',
          utrValueType: UtrValueType.Number,
          value: '',
          Unit: 'm3',
          utrUnit: 'km3',
          utrUnitType: SupportedMeasureUnits.volume,
          skipped: true,
        },
        {
          utrCode: 'numba0',
          utrValueType: UtrValueType.Number,
          value: 0,
          Unit: 'm3',
          utrUnit: 'km3',
          utrUnitType: SupportedMeasureUnits.volume,
        },
      ];

      const dataWithTypeCode = dataWithoutTypeCode.map((utrv) => ({ ...utrv, typeCode: TYPE_CODE_VALUE }));
      const data: MinTransformData[] = [...dataWithoutTypeCode, ...dataWithTypeCode];

      const utrs = data.map(u => {
        return {
          _id: new ObjectId(),
          code: u.utrCode,
          typeCode: u.typeCode,
          valueLabel: u.utrCode,
          valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
          valueValidation: u.utrValueValidation as ValueValidation,
          unit: u.utrUnit,
          unitType: u.unitType,
        } as UtrData
      });

      const result = await dt.transform(data, { ...baseOptions, utrs })

      data.forEach(d => {
        const r = result.find(update => update.utrCode === d.utrCode);

        if (!r) {
          expect(d.skipped).eq(true)
          return
        }
        expect(Boolean(d.skipped), 'expected to be skipped').eq(false)
        expect(r.value).eq(d.value)
        expect(r.valueData?.input?.value).eq(d.value)
        expect(r.valueData?.notApplicableType).eq(d.notApplicableType)
      })
    });

    it('should work with text/date', async () => {
      const dataWithoutTypeCode: MinTransformData[] = [
        {
          utrCode: 'text1',
          utrValueType: UtrValueType.Text,
          value: 'Hello World',
        },
        {
          utrCode: 'text2',
          utrValueType: UtrValueType.Text,
          value: undefined,
          skipped: true, // should be skipped
        },
        {
          utrCode: 'text3-empty-string',
          utrValueType: UtrValueType.Text,
          value: '',
          skipped: true,
        },
        {
          utrCode: 'text4-blank-spaces',
          utrValueType: UtrValueType.Text,
          value: '   ',
          skipped: true,
        },
        {
          utrCode: 'date1',
          utrValueType: UtrValueType.Date,
          value: undefined,
          notApplicableType: NotApplicableTypes.NA
        },
        {
          utrCode: 'date2',
          utrValueType: UtrValueType.Date,
          value: new Date().toISOString(),
        },
      ];
      const dataWithTypeCode = dataWithoutTypeCode.map((utrv) => ({ ...utrv, typeCode: TYPE_CODE_VALUE }));
      const data: MinTransformData[] = [...dataWithoutTypeCode, ...dataWithTypeCode];
      const utrs = data.map(u => {
        return {
          _id: new ObjectId(),
          code: u.utrCode,
          typeCode: u.typeCode,
          valueLabel: u.utrCode,
          valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
          valueValidation: u.utrValueValidation as ValueValidation,
        } as UtrData
      });

      const result = await dt.transform(data, { ...baseOptions, utrs })

      data.forEach(d => {
        const r = result.find(update => update.utrCode === d.utrCode);
        if (!r) {
          expect(d.skipped, `expected to find ${d.utrCode}`).eq(true)
          return
        }
        expect(Boolean(d.skipped), `expected to be skipped ${d.utrCode}`).eq(false)
        expect(r?.valueData?.data).eq(d.value)
        expect(r?.valueData?.input?.data).eq(d.value)
        expect(r?.valueData?.notApplicableType).eq(d.notApplicableType)
      })
    });

    describe('work with existing import columns', () => {
      it('should work with existing import columns with mapping', async () => {
        const data = [
          {
            QuestionCode: 'numba',
            Question: 'Number',
            Value: 2,
            Comment: 'test number',
            Unit: 'Kilowatt-hours',
            expectedInputUnit: 'kWh',
            expectedValue: 0.002,
            utrValueType: UtrValueType.Number,
            utrUnitType: SupportedMeasureUnits.energy,
            utrUnit: 'MWh'
          },
          {
            QuestionCode: 'numba1',
            Question: 'Number with unit code',
            Value: 4,
            Comment: 'test number',
            Unit: 'kWh',
            expectedInputUnit: 'kWh',
            expectedValue: 0.004,
            utrValueType: UtrValueType.Number,
            utrUnitType: SupportedMeasureUnits.energy,
            utrUnit: 'MWh'
          },
          {
            QuestionCode: 'numba2',
            Question: '% question',
            Value: 5,
            Comment: 'test %',
            Unit: 'Kilograms',
            expectedInputUnit: 'kg',
            expectedValue: 5,
            utrValueType: UtrValueType.Percentage,
            utrUnitType: SupportedMeasureUnits.mass,
            utrUnit: 'mt'
          },
          {
            QuestionCode: 'date-column',
            Question: 'date question',
            Value: "2020-01-01",
            Comment: 'test date',
            utrValueType: UtrValueType.Date,
          },
          {
            QuestionCode: 'text-column',
            Question: 'text question',
            Value: "Great!",
            Comment: 'test Text',
            utrValueType: UtrValueType.Text,
          },
          {
            QuestionCode: 'ValueList-column',
            Question: 'ValueList question',
            Value: "yes",
            Comment: 'test ValueList',
            utrValueType: UtrValueType.ValueList,
            utrValueValidation: createValueValidation(yesNoList),
            expectedValue: 'yes',
          },
          {
            QuestionCode: 'ValueList-column-YES',
            Question: 'ValueList question YES',
            Value: "YES",
            Comment: 'test ValueList',
            utrValueType: UtrValueType.ValueList,
            utrValueValidation: createValueValidation(yesNoList),
            expectedValue: 'yes',
          },
          {
            QuestionCode: 'ValueList-custom-yes',
            Question: 'ValueList custom-yes',
            Value: "United Kingdom of Great Britain and Northern Ireland",
            Comment: 'test ValueList',
            utrValueType: UtrValueType.ValueList,
            utrValueValidation: createValueValidation(countryListOne),
            expectedValue: 'gb',
          },
          {
            QuestionCode: 'ValueListMulti-custom-yes',
            Question: 'ValueListMulti custom-yes',
            Value: "United Kingdom of Great Britain and Northern Ireland, us",
            Comment: 'test ValueListMulti',
            utrValueType: UtrValueType.ValueListMulti,
            utrValueValidation: createValueValidation(countryListOne),
            expectedValue: ['gb', 'us'],
          },
          {
            QuestionCode: 'ValueListMulti-custom-yes-NR',
            Question: 'ValueListMulti custom-yes',
            Value: "United Kingdom of Great Britain and Northern Ireland, us",
            Comment: 'test ValueListMulti NR',
            NotApplicableType: 'not_reported',
            utrValueType: UtrValueType.ValueListMulti,
            utrValueValidation: createValueValidation(countryListOne),
            expectedValue: ['gb', 'us'],
          },
        ];

        const utrs = data.map(u => {
          return {
            _id: new ObjectId(),
            code: u.QuestionCode,
            valueLabel: u.Question,
            valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
            valueValidation: u.utrValueValidation as ValueValidation,
            unit: u.utrUnit,
            unitType: u.utrUnitType,
            type: standardCodeGri,
            name,
          }
        });

        const result = await dt.transform(data, { utrs });
        expect(result).to.be.lengthOf(data.length);

        data.forEach((d) => {
          const r = result.find((update) => update.utrCode === d.QuestionCode);
          if (!r) {
            throw new Error(`Failed to find ${d.QuestionCode}`);
          }
          if ([UtrValueType.Number, UtrValueType.Percentage].includes(d.utrValueType)) {
            expect(d.Value).eq(r.valueData?.input?.value);
            expect(d.expectedValue).eq(r.value);
            expect(d.expectedInputUnit).eq(r.valueData?.input?.unit);
          } else if ('NotApplicableType' in d) {
            expect(d.NotApplicableType).eq(r?.valueData?.notApplicableType);
          } else {
            expect(d.expectedValue ?? d.Value).eqls(r.valueData?.data);
            expect(d.expectedValue ?? d.Value).eqls(r.valueData?.input?.data);
          }
          expect(d.Comment).eq(r.note);
        });
      });

      it('should work with existing import columns with Question column change to Metric column', async () => {
        const data = [
          {
            MetricCode: 'numba',
            Metric: 'Number',
            Value: 2,
            Comment: 'test number',
            Unit: 'Kilowatt-hours',
            expectedInputUnit: 'kWh',
            expectedValue: 0.002,
            utrValueType: UtrValueType.Number,
            utrUnitType: SupportedMeasureUnits.energy,
            utrUnit: 'MWh'
          },
          {
            MetricCode: 'numba1',
            Metric: 'Number with unit code',
            Value: 4,
            Comment: 'test number',
            Unit: 'kWh',
            expectedInputUnit: 'kWh',
            expectedValue: 0.004,
            utrValueType: UtrValueType.Number,
            utrUnitType: SupportedMeasureUnits.energy,
            utrUnit: 'MWh'
          },
          {
            MetricCode: 'numba2',
            Metric: '% question',
            Value: 5,
            Comment: 'test %',
            Unit: 'Kilograms',
            expectedInputUnit: 'kg',
            expectedValue: 5,
            utrValueType: UtrValueType.Percentage,
            utrUnitType: SupportedMeasureUnits.mass,
            utrUnit: 'mt'
          },
          {
            MetricCode: 'date-column',
            Metric: 'date question',
            Value: "2020-01-01",
            Comment: 'test date',
            utrValueType: UtrValueType.Date,
          },
          {
            MetricCode: 'text-column',
            Metric: 'text question',
            Value: "Great!",
            Comment: 'test Text',
            utrValueType: UtrValueType.Text,
          },
          {
            MetricCode: 'ValueList-column',
            Metric: 'ValueList question',
            Value: "yes",
            Comment: 'test ValueList',
            utrValueType: UtrValueType.ValueList,
            utrValueValidation: createValueValidation(yesNoList),
            expectedValue: 'yes',
          },
          {
            MetricCode: 'ValueList-column-YES',
            Metric: 'ValueList question YES',
            Value: "YES",
            Comment: 'test ValueList',
            utrValueType: UtrValueType.ValueList,
            utrValueValidation: createValueValidation(yesNoList),
            expectedValue: 'yes',
          },
          {
            MetricCode: 'ValueList-custom-yes',
            Metric: 'ValueList custom-yes',
            Value: "United Kingdom of Great Britain and Northern Ireland",
            Comment: 'test ValueList',
            utrValueType: UtrValueType.ValueList,
            utrValueValidation: createValueValidation(countryListOne),
            expectedValue: 'gb',
          },
          {
            MetricCode: 'ValueListMulti-custom-yes',
            Metric: 'ValueListMulti custom-yes',
            Value: "United Kingdom of Great Britain and Northern Ireland, us",
            Comment: 'test ValueListMulti',
            utrValueType: UtrValueType.ValueListMulti,
            utrValueValidation: createValueValidation(countryListOne),
            expectedValue: ['gb', 'us'],
          },
          {
            MetricCode: 'ValueListMulti-custom-yes-NR',
            Metric: 'ValueListMulti custom-yes',
            Value: "United Kingdom of Great Britain and Northern Ireland, us",
            Comment: 'test ValueListMulti NR',
            NotApplicableType: 'not_reported',
            utrValueType: UtrValueType.ValueListMulti,
            utrValueValidation: createValueValidation(countryListOne),
            expectedValue: ['gb', 'us'],
          },
        ];

        const utrs = data.map(u => {
          return {
            _id: new ObjectId(),
            code: u.MetricCode,
            valueLabel: u.Metric,
            valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
            valueValidation: u.utrValueValidation as ValueValidation,
            unit: u.utrUnit,
            unitType: u.utrUnitType,
            type: standardCodeGri,
            name,
          } as UtrData
        });

        const result = await dt.transform(data, { utrs });
        expect(result).to.be.lengthOf(data.length);

        data.forEach((d) => {
          const r = result.find((update) => update.utrCode === d.MetricCode);
          if (!r) {
            throw new Error(`Failed to find ${d.MetricCode}`);
          }
          if ([UtrValueType.Number, UtrValueType.Percentage].includes(d.utrValueType)) {
            expect(d.Value).eq(r.valueData?.input?.value);
            expect(d.expectedValue).eq(r.value);
            expect(d.expectedInputUnit).eq(r.valueData?.input?.unit);
          } else if ('NotApplicableType' in d) {
            expect(d.NotApplicableType).eq(r?.valueData?.notApplicableType);
          } else {
            expect(d.expectedValue ?? d.Value).eqls(r.valueData?.data);
            expect(d.expectedValue ?? d.Value).eqls(r.valueData?.input?.data);
          }
          expect(d.Comment).eq(r.note);
        });
      });
    });


    describe('import ValueListText and ValueListNumeric questions', function () {
      it('should deal with all empty row values', async () => {

        const dataProvider: ReturnType<typeof createValueListRow>[] = [
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [{ inputValue: '' }, { inputValue: '' }]),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);

        const rowData = dataProvider.reduce((acc, utrData) => {
          const { rows, Question, QuestionCode, } = utrData;
          rows.forEach(r => {
            acc.push({ Question, QuestionCode, ...r })
          })
          return acc;
        }, <unknown[]>[])

        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(0);
      });


      it('should deal with multi row question rows', async function () {

        const dataProvider: ReturnType<typeof createValueListRow>[] = [
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [
            { inputValue: 100, comment: 'Economic value', expectedValue: 100 },
            { inputValue: 100, comment: 'Economic value', expectedValue: 100 },
            { inputValue: 100, comment: 'Third comment', expectedValue: 100 }
          ]),
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [
            { inputValue: 10, comment: 'Only 10', expectedValue: 10 },
          ]),
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [
            { inputValue: '20', comment: 'Only 20 string', expectedValue: '20' },
          ]),
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [
            { inputValue: '', comment: 'Empty string', expectedValue: '' },
          ], undefined, true),
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [
            { inputValue: undefined, comment: 'Empty string', expectedValue: '' },
          ], undefined, true),
          createValueListRow(UtrValueType.TextValueList, griValueListTestOne, [
            { inputValue: 'Direct Value' },
            { inputValue: 'Economic Value', comment: 'Economic value' },
            { inputValue: 'Retained Value' },
          ]),
          createValueListRow(UtrValueType.NumericValueList, valueListTestTable, [
            { inputValue: 10, inputText: 'Cubic kilometers', expectedValue: 10000000000, expectedInputValue: 10, inputUnit: 'km3' },
            { inputValue: 15, inputText: 'Cubic kilometers', expectedValue: 15000000000, expectedInputValue: 15, inputUnit: 'km3' },
            { inputValue: 20, inputText: 'Cubic kilometers', expectedValue: 20000000000, expectedInputValue: 20, inputUnit: 'km3' },
            { inputValue: 25, inputText: 'Cubic kilometers', expectedValue: 25000000000, expectedInputValue: 25, inputUnit: 'km3' },
            { inputValue: 30, inputText: 'Cubic kilometers', expectedValue: 30000000000, expectedInputValue: 30, inputUnit: 'km3' },
          ], { unit: "m3", unitType: SupportedMeasureUnits.volume }),
          createValueListRow(UtrValueType.NumericValueList, valueListTestTable, [
            { inputValue: 2, inputText: 'Cubic meters', expectedValue: 2, expectedInputValue: 2, inputUnit: 'm3' },
            { inputValue: 3, inputText: 'Cubic kilometers', expectedValue: 3000000000, expectedInputValue: 3000000000, inputUnit: 'm3' },
            { inputValue: 4, inputText: 'Megalitres', expectedValue: 4000, expectedInputValue: 4000, inputUnit: 'm3' },
            { inputValue: 5, inputText: 'Cubic kilometers', expectedValue: 5000000000, expectedInputValue: 5000000000, inputUnit: 'm3' },
            { inputValue: 6, inputText: 'Kilolitres', expectedValue: 6, expectedInputValue: 6, inputUnit: 'm3' },
          ], { unit: "m3", unitType: SupportedMeasureUnits.volume }),
          createValueListRow(UtrValueType.NumericValueList, valueListTestTable, [
            { inputValue: 4, comment: 'all options must be in the same unit', inputText: 'Megalitres', expectedValue: 4000, expectedInputValue: 4, inputUnit: 'Ml' },
          ], { unit: "m3", unitType: SupportedMeasureUnits.volume }),
          createValueListRow(UtrValueType.NumericValueList, valueListTestTable, [
            { inputValue: 4, comment: 'all options must be in the same unit', inputText: 'Megalitres', expectedValue: 4000, expectedInputValue: 4000, inputUnit: 'm3' },
            { inputValue: 4, comment: 'if no input text it use the utr unit', expectedValue: 4, expectedInputValue: 4, inputUnit: 'm3' },
          ], { unit: "m3", unitType: SupportedMeasureUnits.volume }),
        ];

        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);

        const rowData = dataProvider.reduce((acc, utrData) => {
          const { rows, Question, QuestionCode, } = utrData;
          rows.forEach(r => {
            acc.push({ Question, QuestionCode, ...r })
          })
          return acc;
        }, <unknown[]>[]);

        const result = await dt.transform(rowData, { utrs })
        dataProvider.forEach(d => {
          const r = result.find(update => update.utrCode === d.QuestionCode);

          if (!r) {
            expect(d.skip).eqls(true);
            return;
          }

          const { data, dataInput, note, value, inputValue } = d.rows.reduce((a, row) => {

            if (row.Value === '' || row.Value === undefined) {
              return a;
            }

            a.data[row.OptionCode] = row.Value;
            a.dataInput[row.OptionCode] = row.Value;
            if (typeof row.Comment === 'string' && row.Comment) {
              a.note.push(row.Comment)
            }

            if (d.utrValueType === UtrValueType.NumericValueList) {
              a.data[row.OptionCode] = row.ExpectedValue;
              a.dataInput[row.OptionCode] = row.ExpectedInputValue ?? row.Value;
              a.value = (a.value ?? 0) + Number(row.ExpectedValue);
              a.inputValue = (a.inputValue ?? 0) + Number(row.ExpectedInputValue ?? row.Value);
            }

            return a;
          }, <ExpectedResult>{ data: {}, dataInput: {}, note: [], value: undefined, inputValue: undefined});

          expect(r.valueData?.data).eqls(data);
          expect(r.valueData?.input?.data).eqls(dataInput);
          const comments = note.length ? note.join("\n") : undefined;
          expect(r.note).eq(comments)

          if (d.utrValueType === UtrValueType.NumericValueList) {
            expect(r.value).eq(value)
            expect(Number(r.valueData?.input?.value)).eq(inputValue)
            d.rows.forEach(item => {
              expect(r.valueData?.input?.unit).eq(item.InputUnit);
            })
          }
        });
      });
    });


    describe('transform table', function () {

      it('should deal with empty tables', async function () {

        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: '' }),
            createColumn({ type: ColumnType.Number }),
            createColumn({ type: ColumnType.Number }),
            createColumn({ value: '' }),
          ], 'single-row-table', 1),
          createRow([
            createColumn({ value: '' }),
            createColumn({ type: ColumnType.Number, value: '' }),
            createColumn({ type: ColumnType.Number, value: '' }),
            createColumn({ value: '' }),
          ], 'multi-row-utr'),
          createRow([
            createColumn({ code: 'n1', value: '        ' }),
            createColumn({ code: 'n2', type: ColumnType.Number, value: undefined }),
            createColumn({ code: 'n3', type: ColumnType.Number, value: undefined }),
          ], 'multi-row-utr'),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)
        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(0);
      });

      it('should validate value for column with options', async function () {

        const [firstOption] = countryListOne.options;
        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: firstOption.code, listId: countryListOne._id }),
            createColumn({ value: firstOption.name, listId: countryListOne._id }),
            createColumn({ value: '', listId: countryListOne._id }),
            createColumn({ value: 'invalid-code', listId: countryListOne._id }),
          ], 'single-row-table', 1),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)
        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(1);
        const valueData = result[0]?.valueData;
        expect(valueData?.table?.[0]).lengthOf(2);
        expect(valueData?.input?.table?.[0]).lengthOf(2);
      });

      it('should validate value for column with options values with case insensitive way', async function () {
        const [firstOption] = countryListOne.options;
        const dataProvider: DataTableProvider[] = [
          createRow([
            // This should fail
            createColumn({ value: firstOption.code.toUpperCase(), listId: countryListOne._id }),
            // this should pass
            createColumn({ value: firstOption.name, listId: countryListOne._id }),
            createColumn({ value: firstOption.name.toUpperCase(), listId: countryListOne._id }),
            createColumn({ value: firstOption.name.toLowerCase(), listId: countryListOne._id }),
          ], 'single-row-table', 1),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)
        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(1);
        const firstRow = result[0]?.valueData?.table?.[0] ?? [];
        expect(firstRow).lengthOf(3);
      });

      it('should validate value for column with custom options if allowCustomOptions is true', async function () {
        const [firstOption] = countryListOne.options;
        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: '', listId: countryListOne._id, validation: { allowCustomOptions: true } }),
            createColumn({ value: 'invalid-code', listId: countryListOne._id, validation: { allowCustomOptions: false } }),
            // this should pass
            createColumn({ value: firstOption.name, listId: countryListOne._id }),
            createColumn({ value: firstOption.name.toUpperCase(), listId: countryListOne._id }),
            createColumn({ value: 'custom-code', listId: countryListOne._id, validation: { allowCustomOptions: true } }),
          ], 'single-row-table', 1),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)
        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(1);
        const firstRow = result[0]?.valueData?.table?.[0] ?? [];
        expect(firstRow).lengthOf(3);
        expect(firstRow[2].value).to.be.eq('custom-code');
      });

      it('should skip value for column with options but numeric value', async function () {

        const [firstOption] = countryListOne.options;
        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: firstOption.code, listId: countryListOne._id }),
            createColumn({ value: 123, listId: countryListOne._id }),
          ], 'single-row-table', 1),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)
        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(1);
        const firstRow = result[0]?.valueData?.table?.[0] ?? [];
        expect(firstRow).lengthOf(1);
      });

      it('support array of values with options', async function () {

        const [firstOption, secondOption] = countryListOne.options;
        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: `${firstOption.code}, ${secondOption.code}`, listId: countryListOne._id }),
          ], 'single-row-table', 1),
        ];
        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)
        const result = await dt.transform(rowData, { utrs })
        expect(result).to.be.lengthOf(1);
        const firstRow = result[0]?.valueData?.table?.[0] ?? [];
        expect(firstRow).lengthOf(1);
        expect(firstRow[0].value).lengthOf(2);
      });

      it('should transform table questions', async function () {

        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: 'yes' }),
            createColumn({ type: ColumnType.Number, value: 50 }),
            createColumn({ type: ColumnType.Number, value: 10 }),
            createColumn({ value: 'no' }),
          ], 'single-row-table', 1),
          createRow([
            createColumn({ code: 'n1', value: 'yes' }),
            createColumn({ code: 'n2', type: ColumnType.Number, value: 10 }),
            createColumn({ code: 'n3', type: ColumnType.Number, value: 10 }),
          ], 'multi-row-utr'),
          createRow([
            createColumn({ code: 'n1', value: 'no' }),
            createColumn({ code: 'n2', type: ColumnType.Number, value: 10 }),
            createColumn({ code: 'n3', type: ColumnType.Number, value: undefined }),
            createColumn({ code: 'n4', value: 'no' }),
          ], 'multi-row-utr'),
        ];

        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider)

        const result = await dt.transform(rowData, { utrs })
        const map = convertDataProviderToMap(dataProvider);

        Array.from(map.values()).forEach((d) => {
          const r = result.find(update => update.utrCode === d.QuestionCode);
          if (!r) {
            throw new Error(`Failed to find ${d.QuestionCode}`);
          }

          const { table, inputTable, note } = d.rows.reduce((a, row) => {
            if (row.Value === '' || row.Value === undefined) {
              return a;
            }

            a.table.push({ code: row.OptionCode, value: row.Value});
            a.inputTable.push({ code: row.OptionCode, value: row.Value, unit: undefined});
            if (typeof row.Comment === 'string' && row.Comment) {
              a.note.push(row.Comment)
            }
            return a;
          }, <ExpectedTableResult>{ table: [], inputTable: [], note: []});

          expect(table).eqls(r.valueData?.table?.flat());
          expect(inputTable).eqls(r.valueData?.input?.table?.flat());
          const comments = note.length ? note.join("\n"): undefined;
          expect(comments).eq(r.note)
        });
      });

      it('should transform table questions with modified unit type', async function () {

        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ code: 'n1', value: 'yes' }),
            createColumn({ code: 'n2', type: ColumnType.Number, value: 50, importUnit: 'Kilojoules', unit: 'J', unitType: SupportedMeasureUnits.energy }),
            createColumn({ code: 'n3', type: ColumnType.Number, value: 2, importUnit: 'Kilograms', unit: 'mt', unitType: SupportedMeasureUnits.mass }),
            createColumn({ code: 'n4', type: ColumnType.Number, value: 1, importUnit: 'Kilowatt-hours', unit: 'MWh', unitType: SupportedMeasureUnits.energy }),
          ], 'single-row-table', 1),
          createRow([
            createColumn({ code: 'multi-n1', value: 'yes' }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 10, importUnit: 'Tons', unit: 'kg', unitType: SupportedMeasureUnits.mass }),
            createColumn({ code: 'multi-n3', type: ColumnType.Number, value: 20000, importUnit: 'Litres', unit: 'm3', unitType: SupportedMeasureUnits.volume }),
            createColumn({ code: 'multi-n4', type: ColumnType.Number, value: 10000, importUnit: 'Kilojoules', unit: 'mJ', unitType: SupportedMeasureUnits.energy }),
          ], 'multi-row-utr'),
          createRow([
            createColumn({ code: 'multi-n1', value: 'no' }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 3, importUnit: 'Metric Tonnes', unit: 'kg', unitType: SupportedMeasureUnits.mass }),
            createColumn({ code: 'multi-n3', type: ColumnType.Number, value: 450, importUnit: 'Megalitres', unit: 'm3', unitType: SupportedMeasureUnits.volume }),
            createColumn({ code: 'multi-n4', type: ColumnType.Number, value: 6.5, importUnit: 'Megawatt-hours', unit: 'mJ', unitType: SupportedMeasureUnits.energy }),
          ], 'multi-row-utr'),
        ];

        const expectedProcessedData = {
          'single-row-table': {
            convertedValue: [
              [
                { code: 'n1', value: 'yes' },
                { code: 'n2', value: 50000 },
                { code: 'n3', value: 0.002 },
                { code: 'n4', value: 0.001 },
              ],
            ],
            inputValue: [
              [
                { code: 'n1', value: 'yes', unit: undefined },
                { code: 'n2', value: 50, unit: 'kJ' },
                { code: 'n3', value: 2, unit: 'kg' },
                { code: 'n4', value: 1, unit: 'kWh' },
              ],
            ]
          },
          'multi-row-utr': {
            convertedValue: [
              [
                { code: 'multi-n1', value: 'yes' },
                { code: 'multi-n2', value: 9071.84 },
                { code: 'multi-n3', value: 20 },
                { code: 'multi-n4', value: 10 },
              ],
              [
                { code: 'multi-n1', value: 'no' },
                { code: 'multi-n2', value: 3000 },
                { code: 'multi-n3', value: 450000 },
                { code: 'multi-n4', value: 23400 },
              ],
            ],
            inputValue: [
              [
                { code: 'multi-n1', value: 'yes', unit: undefined },
                { code: 'multi-n2', value: 10, unit: 't' },
                { code: 'multi-n3', value: 20000, unit: 'l' },
                { code: 'multi-n4', value: 10000, unit: 'kJ' },
              ],
              [
                { code: 'multi-n1', value: 'no', unit: undefined },
                { code: 'multi-n2', value: 3, unit: 'mt' },
                { code: 'multi-n3', value: 450, unit: 'Ml' },
                { code: 'multi-n4', value: 6.5, unit: 'MWh' },
              ],
            ],
          },
        };

        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider);

        const result = await dt.transform(rowData, { utrs });
        const map = convertDataProviderToMap(dataProvider);

        Array.from(map.values()).forEach((d) => {
          const questionCode = d.QuestionCode as keyof typeof expectedProcessedData;
          const r = result.find((update) => update.utrCode === questionCode);
          if (!r) {
            throw new Error(`Failed to find ${questionCode}`);
          }

          expect(r.valueData?.table).eqls(expectedProcessedData[questionCode].convertedValue);
          expect(r.valueData?.input?.table).eqls(expectedProcessedData[questionCode].inputValue);
        });
      });

      it('should transform table questions with calculation columns', async function () {
        const dataProvider: DataTableProvider[] = [
          createRow(
            [
              createColumn({ code: 'n1', value: 'yes' }),
              createColumn({ code: 'n2', type: ColumnType.Number, value: 50 }),
              createColumn({ code: 'n3', type: ColumnType.Number, value: 10 }),
              createColumn({
                code: 'n4',
                type: ColumnType.Number,
                calculation: {
                  formula: '{n2}/{n3}',
                },
              }),
            ],
            'single-row-table',
            1
          ),
          createRow(
            [
              createColumn({ code: 'n1-value', type: ColumnType.Number, value: 50 }),
              createColumn({ code: 'n2-value', type: ColumnType.Number, value: 10 }),
              createColumn({
                code: 'n3-value',
                value: '2',
                type: ColumnType.Number,
                calculation: {
                  formula: '{n1-value}/{n2-value}',
                },
              }),
            ],
            'single-row-table-existing-value',
            1
          ),
          createRow(
            [
              createColumn({ code: 'multi-n1', value: 'yes' }),
              createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 10 }),
              createColumn({ code: 'multi-n3', type: ColumnType.Number, value: 20 }),
              createColumn({ code: 'multi-n4', type: ColumnType.Number, value: 5 }),
              createColumn({ code: 'multi-n5', type: ColumnType.Number, value: 30 }),
              createColumn({
                code: 'multi-n6',
                type: ColumnType.Number,
                calculation: {
                  formula: '{multi-n2}*{multi-n3}-{multi-n5}/{multi-n4}',
                },
              }),
            ],
            'multi-row-utr'
          ),
          createRow(
            [
              createColumn({ code: 'multi-n1', value: 'no' }),
              createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 20 }),
              createColumn({ code: 'multi-n3', type: ColumnType.Number, value: 50 }),
              createColumn({ code: 'multi-n4', type: ColumnType.Number, value: 20 }),
              createColumn({ code: 'multi-n5', type: ColumnType.Number, value: 100 }),
              createColumn({
                code: 'multi-n6',
                type: ColumnType.Number,
                calculation: {
                  formula: '{multi-n2}*{multi-n3}-{multi-n5}/{multi-n4}',
                },
              }),
            ],
            'multi-row-utr'
          ),
          createRow(
            [
              createColumn({ code: 'multi-n1', value: 'no' }),
              createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 30 }),
              createColumn({ code: 'multi-n3', type: ColumnType.Number, value: 10 }),
              createColumn({ code: 'multi-n4', type: ColumnType.Number, value: 20 }),
              createColumn({ code: 'multi-n5', type: ColumnType.Number, value: 400 }),
              createColumn({
                code: 'multi-n6',
                type: ColumnType.Number,
                calculation: {
                  formula: '{multi-n2}*{multi-n3}-{multi-n5}/{multi-n4}',
                },
              }),
            ],
            'multi-row-utr'
          ),
          createRow(
            [
              createColumn({ code: 'multi-n1', type: ColumnType.Number, value: 20 }),
              createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 50 }),
              createColumn({
                code: 'multi-n3',
                type: ColumnType.Number,
                value: 900,
                calculation: {
                  formula: '{multi-n1}*{multi-n2}',
                },
              }),
            ],
            'multi-row-calculation-with-value'
          ),
          createRow(
            [
              createColumn({ code: 'multi-n1', type: ColumnType.Number, value: '20' }),
              createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 50 }),
              createColumn({
                code: 'multi-n3',
                type: ColumnType.Number,
                value: 900,
                calculation: {
                  formula: '{multi-n1}*{multi-n2}',
                },
              }),
            ],
            'multi-row-calculation-with-string-value'
          ),

        ];

        const expectedProcessedData: Record<string, TableData> = {
          'single-row-table': [
            [
              { code: 'n1', value: 'yes' },
              { code: 'n2', value: 50 },
              { code: 'n3', value: 10 },
              { code: 'n4', value: 5 },
            ],
          ],
          'single-row-table-existing-value' : [
            [
              { code: 'n1-value', value: 50 },
              { code: 'n2-value', value: 10 },
              { code: 'n3-value', value: 5 },
            ],
          ],
          'multi-row-utr': [
            [
              { code: 'multi-n1', value: 'yes' },
              { code: 'multi-n2', value: 10 },
              { code: 'multi-n3', value: 20 },
              { code: 'multi-n4', value: 5 },
              { code: 'multi-n5', value: 30 },
              { code: 'multi-n6', value: 194 },
            ],
            [
              { code: 'multi-n1', value: 'no' },
              { code: 'multi-n2', value: 20 },
              { code: 'multi-n3', value: 50 },
              { code: 'multi-n4', value: 20 },
              { code: 'multi-n5', value: 100 },
              { code: 'multi-n6', value: 995 },
            ],
            [
              { code: 'multi-n1', value: 'no' },
              { code: 'multi-n2', value: 30 },
              { code: 'multi-n3', value: 10 },
              { code: 'multi-n4', value: 20 },
              { code: 'multi-n5', value: 400 },
              { code: 'multi-n6', value: 280 },
            ],
          ],
          'multi-row-calculation-with-value': [
            [
              { code: 'multi-n1', value: 20 },
              { code: 'multi-n2', value: 50 },
              { code: 'multi-n3', value: 1000 },
            ],
          ],
          'multi-row-calculation-with-string-value': [
            [
              // Not actually converting strings to number, even we probably should
              { code: 'multi-n1', value: "20" },
              { code: 'multi-n2', value: 50 },
              // Calculation should still work
              { code: 'multi-n3', value: 1000 },
            ],
          ]
        };

        const utrs = createUtrs([...dataProvider, dataProvider.map((item) => ({ ...item, TypeCode: TYPE_CODE_VALUE }))]);
        const rowData = reduceToRowData(dataProvider);

        const result = await dt.transform(rowData, { utrs });
        const map = convertDataProviderToMap(dataProvider);

        Array.from(map.values()).forEach((d) => {
          const r = result.find((update) => update.utrCode === d.QuestionCode);
          if (!r) {
            throw new Error(`Failed to find ${d.QuestionCode}`);
          }

          expect(r.valueData?.table).eqls(expectedProcessedData[d.QuestionCode]);
          expect(r.valueData?.input?.table).eqls(expectedProcessedData[d.QuestionCode]);
        });
      });
    });

    describe('custom unit text', function () {
      it('should add custom unit text to note for simple utr', async function () {
        const data: MinTransformData[] = [
          {
            utrCode: 'numba',
            utrValueType: UtrValueType.Number,
            value: 2,
            unit: 'Invalid unit',
            note: 'test number',
          },
          {
            utrCode: 'numbaAA',
            utrValueType: UtrValueType.Percentage,
            value: undefined,
            unit: 'Invalid unit #2',
            note: 'test percentage',
          },
          {
            utrCode: 'numba0',
            utrValueType: UtrValueType.Text,
            value: 'test',
            unit: 'Invalid unit #3',
            note: 'test text',
          },
        ];

        const utrs = data.map((u) => {
          return {
            _id: new ObjectId(),
            code: u.utrCode,
            typeCode: u.typeCode,
            valueLabel: u.utrCode,
            valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
            valueValidation: u.utrValueValidation as ValueValidation,
            unit: u.utrUnit,
            unitType: u.unitType,
          } as UtrData;
        });

        const result = await dt.transform(data, { ...baseOptions, utrs });

        data.forEach((d) => {
          const r = result.find((update) => update.utrCode === d.utrCode);

          if (!r) {
            return;
          }
          expect(r.value ?? r.valueData?.data).eq(d.value);
          expect(r.valueData?.input?.value ?? r?.valueData?.input?.data).eq(d.value);
          expect(r.valueData?.notApplicableType).eq(d.notApplicableType);
          expect(r.note).eq(`Unit: ${d.unit}\n${d.note}`);
        });
      });
      it('should add custom unit text to note for table utrs', async function () {
        const dataProvider: DataTableProvider[] = [
          createRow([
            createColumn({ value: 'yes', importUnit: 'Invalid unit #1' }),
            createColumn({ type: ColumnType.Number, comment: 'Comment #2', value: 50, importUnit: 'Invalid unit #2' }),
            createColumn({ type: ColumnType.Number, value: 10 }),
            createColumn({ value: 'no', comment: 'Comment #2' }),
          ], 'single-row-table', 1),
          createRow([
            createColumn({ code: 'n1', value: 'yes' }),
            createColumn({ code: 'n2', type: ColumnType.Number, value: 10, importUnit: 'Invalid unit #3' }),
            createColumn({ code: 'n3', type: ColumnType.Number, value: 10, importUnit: 'Invalid unit #4' }),
          ], 'multi-row-utr'),
        ];

        const utrs = createUtrs(dataProvider);
        const rowData = reduceToRowData(dataProvider);

        const result = await dt.transform(rowData, { utrs })
        const map = convertDataProviderToMap(dataProvider);

        Array.from(map.values()).forEach((d) => {
          const r = result.find((update) => update.utrCode === d.QuestionCode);
          const note = d.rows.reduce((a, row) => {
            if (row.Value === '' || row.Value === undefined) {
              return a;
            }
            const parts = [];
            if (row.Unit) {
              parts.push(`Unit: ${row.Unit}`);
            }
            if (a) {
              parts.push(a);
            }
            if (row.Comment) {
              parts.push(row.Comment);
            }
            return parts.join('\n');
          }, '');

          expect(note).eq(r?.note);
        });
      });
      it('should add custom unit text to note for value list utrs', async function () {
        const dataProvider: ReturnType<typeof createValueListRow>[] = [
          createValueListRow(UtrValueType.NumericValueList, countryListOne, [
            { inputValue: 100, comment: 'Economic value', expectedValue: 100, inputText: 'Invalid unit #1' },
            { inputValue: 100, comment: 'Economic value', expectedValue: 100 },
            { inputValue: 100, comment: 'Third comment', expectedValue: 100, inputText: 'Invalid unit #2' },
          ]),
          createValueListRow(UtrValueType.TextValueList, griValueListTestOne, [
            { inputValue: 'Direct Value' },
            { inputValue: 'Economic Value', comment: 'Economic value', inputText: 'Invalid unit #3' },
            { inputValue: 'Retained Value' },
          ]),
        ];

        const utrs = createUtrs(dataProvider);

        const rowData = dataProvider.reduce((acc, utrData) => {
          const { rows, Question, QuestionCode, } = utrData;
          rows.forEach(r => {
            acc.push({ Question, QuestionCode, ...r })
          })
          return acc;
        }, <unknown[]>[]);

        const result = await dt.transform(rowData, { utrs })
        dataProvider.forEach(d => {
          const r = result.find(update => update.utrCode === d.QuestionCode);
          const note = d.rows.reduce((a, row) => {
            if (row.Value === '' || row.Value === undefined) {
              return a;
            }
            const parts = [];
            if (row.Unit) {
              parts.push(`Unit: ${row.Unit}`);
            }
            if (a) {
              parts.push(a);
            }
            if (row.Comment) {
              parts.push(row.Comment);
            }
            return parts.join('\n');
          }, '');
          expect(note).eq(r?.note);
        });
      });
    });
  });


  describe('transform to row', function () {
    const altTypeCode = 'gri-custom';
    const altName = 'test alternative name';
    const valueLabel = 'test label';
    const alternatives = { sgx_metrics: { name: altName, valueLabel, typeCode: altTypeCode } };

    it('should set not applicable type', async () => {
      const dataProvider: [UtrValueType, NotApplicableTypes][] = [
        [UtrValueType.Number, NotApplicableTypes.NA],
        [UtrValueType.Percentage, NotApplicableTypes.NR],
        [UtrValueType.ValueList, NotApplicableTypes.NR],
      ];
      const transformData = dataProvider.map(([valueType, notApplicableType], index) => {
        const utrCode = `${valueType}-${index}`;
        return createCombinedFromCode(
          utrCode,
          { valueData: { notApplicableType } },
          {
            valueType,
            valueLabel: utrCode,
            alternatives,
            type: standardCodeGri,
            name,
          }
        );
      });

      const data = await dt.transformToRows(transformData, {
        utrs: transformData.map(v => (v.universalTracker as UtrData)),
        preferredTypes: ['sgx_metrics'],
      })

      const dataWithTypeCode = [...data, ...data.map(utr => ({...utr, typeCode: TYPE_CODE_VALUE}))];
      transformData.forEach((utrv, i) => {
        const questionRow = dataWithTypeCode[i];
        expect(utrv.universalTracker?.code).to.be.eq(questionRow.utrCode)
        expect(utrv.universalTracker?.alternatives?.sgx_metrics?.typeCode).to.be.eq(questionRow.typeCode)
        expect(utrv.valueData?.notApplicableType).to.be.eq(questionRow.notApplicableType)
        expect(questionRow.utrId).not.to.be.empty
        expect(questionRow.utrvId).not.to.be.empty
      });

      data.forEach((utrv) => {
        expect(utrv.typeCode).to.be.eq(altTypeCode);
        expect(utrv.question).to.be.eq(valueLabel);
      });
    });

    it('simple numeric types', async () => {
      const dataProvider: [UtrValueType, number?, string?][] = [
        [UtrValueType.Number],
        [UtrValueType.Number, 100],
        [UtrValueType.Sample, 15, 'MWh'],
        [UtrValueType.Percentage, 10, 'MWh'],
      ];
      const transformData = dataProvider.map(([valueType, value, unit], index) => {
        const utr = createUtrFromCode(`${valueType}-${index}`, { unit, alternatives, type: standardCodeGri, name });
        return createUtrv(new ObjectId(), utr._id, value, { universalTracker: utr });
      });

      const data = await dt.transformToRows(transformData, {
        utrs: transformData.map(v => (v.universalTracker as UtrData)),
        preferredTypes: ['sgx_metrics'],
      })
      const dataWithTypeCode = [...data, ...data.map(utr => ({...utr, typeCode: TYPE_CODE_VALUE}))];
      transformData.forEach((utrv, i) => {
        const questionRow = dataWithTypeCode[i];
        const expectedValue = utrv.value ? getNumericCellFormat({ value: utrv.value }) : undefined;
        expect(questionRow.value).to.deep.eq(expectedValue);
        expect(utrv.universalTracker?.code).to.be.eq(questionRow.utrCode);
        expect(utrv.universalTracker?.alternatives?.sgx_metrics?.typeCode).to.be.eq(questionRow.typeCode);
        expect(utrv.universalTracker?.unit).to.be.eq(questionRow.unit);
      })
      data.forEach((utrv) => {
        expect(utrv.typeCode).to.be.eq(altTypeCode);
        expect(utrv.question).to.be.eq(valueLabel);
      });
    });

    it('text/date strings types', async () => {
      const dataProvider: [UtrValueType, (string | number)?][] = [
        [UtrValueType.Text],
        [UtrValueType.Text, 'This is really strange!'],
        [UtrValueType.Date],
        [UtrValueType.Date, '2020-10-10'],
      ];
      const transformData = dataProvider.map(([valueType, value], index) => {
        const utrCode = `${valueType}-${index}`;
        return createCombinedFromCode(
          utrCode,
          { valueData: { data: value } },
          { valueType, valueLabel: utrCode, alternatives, type: standardCodeGri, name })
      });

      const data = await dt.transformToRows(transformData, {
        utrs: transformData.map(v => v.universalTracker),
        preferredTypes: ['sgx_metrics'],
      })
      const dataWithTypeCode = [...data, ...data.map(utr => ({...utr, typeCode: TYPE_CODE_VALUE}))];
      transformData.forEach((utrv, i) => {
        const questionRow = dataWithTypeCode[i];
        expect(utrv.valueData?.data).to.be.eq(questionRow.value);
        expect(utrv.universalTracker.valueType).to.be.eq(questionRow.valueType);
        expect(utrv.universalTracker.code).to.be.eq(questionRow.utrCode);
        expect(utrv.universalTracker?.alternatives?.sgx_metrics?.typeCode).to.be.eq(questionRow.typeCode);
      });
      data.forEach((utrv) => {
        expect(utrv.typeCode).to.be.eq(altTypeCode);
        expect(utrv.question).to.be.eq(valueLabel);
      });
    });


    it('valueList/valueListMulti strings types', async () => {
      const valueValidation = createValueValidation(yesNoList);
      const country = createValueValidation(countryListOne);
      const dataProvider: [UtrValueType, ValueValidation, (string | string[] | number | undefined)?][] = [
        [UtrValueType.ValueList, valueValidation],
        [UtrValueType.ValueList, valueValidation, valueValidation.valueList?.list?.[0]?.code],
        [UtrValueType.ValueListMulti, country],
        [UtrValueType.ValueListMulti, country, ['gb', 'us']],
        [UtrValueType.ValueListMulti, country, 0],
        [UtrValueType.ValueListMulti, country, undefined],
      ];
      const transformData = dataProvider.map((data, index) => {
        const [valueType, valueValidation, value] = data;
        const utrCode = `${valueType}-${index}`;
        return createCombinedFromCode(
          utrCode,
          { valueData: { data: value } },
          { valueType, valueLabel: utrCode, valueValidation, alternatives, type: standardCodeGri, name })
      });

      const data = await dt.transformToRows(transformData, {
        utrs: transformData.map(v => v.universalTracker),
        preferredTypes: ['sgx_metrics'],
      })
      const dataWithTypeCode = [...data, ...data.map(utr => ({...utr, typeCode: TYPE_CODE_VALUE}))];

      transformData.forEach((utrv, i) => {
        const questionRow = dataWithTypeCode[i];
        const valueData = utrv.valueData?.data;
        const options = utrv.universalTracker.valueValidation?.valueList?.list || [];
        if (typeof valueData === 'number') {
          expect(questionRow.value).to.be.eq(undefined);
        } else if (Array.isArray(valueData)) {
          expect(valueData?.join(', ')).to.be.eq(
            String(questionRow.value)
              .split(', ')
              .map((v) => options.find(({ name }) => name === v)?.code)
              .join(', ')
          );
        } else {
          expect(valueData).to.be.eq(options.find(({ name }) => name === questionRow.value)?.code);
        }
        expect(utrv.universalTracker.code).to.be.eq(questionRow.utrCode);
        expect(utrv.universalTracker?.alternatives?.sgx_metrics?.typeCode).to.be.eq(questionRow.typeCode);
        expect(options).to.be.an('array');
        expect(options).to.be.eqls(questionRow.options);
        expect(utrv.universalTracker.valueType).to.be.eq(questionRow.valueType);
      });
      data.forEach((utrv) => {
        expect(utrv.typeCode).to.be.eq(altTypeCode);
        expect(utrv.question).to.be.eq(valueLabel);
      });
    });


    it('NumericValueList/TextValueList strings types', async () => {
      const griValueList = createValueValidation(griValueListTestOne);
      const country = createValueValidation(countryListOne);
      type ValueType = [string, string | number];
      const dataProvider: [UtrValueType, ValueValidation, (ValueType[])?][] = [
        [UtrValueType.NumericValueList, griValueList],
        [UtrValueType.NumericValueList, griValueList, [
          [griValueList.valueList?.list?.[0]?.code as string, 30],
        ]],
        [UtrValueType.TextValueList, country],
        [UtrValueType.TextValueList, country, [
          ['gb', 'This is text'],
          ['us', 'Second Text'],
        ]],
      ];
      const transformData = dataProvider.map((data, index) => {
        const [valueType, valueValidation, value] = data;
        const utrCode = `${valueType}-${index}`;
        return createCombinedFromCode(
          utrCode,
          { valueData: { data: value } },
          { valueType, valueLabel: utrCode, valueValidation, alternatives, type: standardCodeGri, name })
      });

      const data = await dt.transformToRows(transformData, {
        utrs: transformData.map(v => v.universalTracker),
        preferredTypes: ['sgx_metrics'],
      })
      const dataWithTypeCode = [...data, ...data.map(utr => ({...utr, typeCode: TYPE_CODE_VALUE}))];

      let nextQuestionIndex = 0;
      transformData.forEach((utrv) => {
        const options = utrv.universalTracker.valueValidation?.valueList?.list;
        options?.forEach((op, index) => {

          const questionRow = dataWithTypeCode[nextQuestionIndex];
          nextQuestionIndex++;
          if (index === 0) {
            expect(utrv.note).to.be.eq(questionRow.note)
          }

          expect(op.code).to.be.eq(questionRow.columnCode)
          expect(op.name).to.be.eq(questionRow.columnName)
          expect(utrv.valueData?.data?.[op.code]).to.be.eq(questionRow.value)
          expect(utrv.universalTracker.valueType).to.be.eq(questionRow.valueType);
        })
      });
      data.forEach((utrv) => {
        expect(utrv.typeCode).to.be.eq(altTypeCode);
        expect(utrv.question).to.be.eq(valueLabel);
      });
    });

    describe('table type', function () {

      type DataProvider = [ValueValidation, ColumnTestData[][], ][];

      const createTransformData = (dataProvider: DataProvider) => {
        return dataProvider.map((data, index) => {
          const [valueValidation, value] = data;
          const valueType = UtrValueType.Table;
          const utrCode = `${valueType}-${index}`;
          return createCombinedFromCode(
            utrCode,
            { valueData: { table: value }, note: value[0]?.[0].comment },
            { valueType, valueLabel: utrCode, valueValidation, alternatives, type: standardCodeGri, name })
        });
      };

      it('should deal with empty table', async () => {
        const dataProvider: DataProvider = [[createTableValidation([]), []]];
        const transformData = createTransformData(dataProvider);
        const data = await dt.transformToRows(transformData, {
          utrs: transformData.map(v => v.universalTracker),
          preferredTypes: ['sgx_metrics'],
        })
        expect(data).to.be.lengthOf(0);
        data.forEach((utrv) => {
          expect(utrv.typeCode).to.be.eq(altTypeCode);
          expect(utrv.question).to.be.eq(valueLabel);
        });
      });

      it('handle single row table', async () => {

        const cols = [
          createColumn({ value: 'yes', unit: 'm3' }),
          createColumn({ type: ColumnType.Number, value: 50, unit: 'km3' }),
        ];
        const colsSecondRow = [
          createColumn({ value: 'no' }),
          createColumn({ type: ColumnType.Number, value: 150 }),
        ];

        const dataProvider: DataProvider = [
          [createTableValidation(cols, 1), [cols]],
          [createTableValidation(colsSecondRow, 1), [colsSecondRow]],
        ];
        const transformData = createTransformData(dataProvider);

        const data = await dt.transformToRows(transformData, {
          utrs: transformData.map(v => v.universalTracker),
          preferredTypes: ['sgx_metrics'],
        })
        let nextQuestionIndex = 0;
        transformData.forEach((utrv) => {
          const columns = utrv.universalTracker.valueValidation?.table?.columns;
          columns?.forEach((column, index) => {

            const questionRow = data[nextQuestionIndex];
            nextQuestionIndex++;
            if (index === 0) {
              expect(utrv.note).to.be.eq(questionRow.note)
            }

            expect(column.unit).to.be.eq(questionRow.unit)
            expect(column.code).to.be.eq(questionRow.columnCode)
            expect(column.name).to.be.eq(questionRow.columnName)
            const firstRow = utrv.valueData?.table?.[0];
            expect(firstRow?.find(col => col.code === column.code)?.value).to.be.eq((questionRow.values || [])[0])
            expect(column.type).to.be.eq(questionRow.valueType);
          })
        });
        data.forEach((utrv) => {
          expect(utrv.typeCode).to.be.eq(altTypeCode);
          expect(utrv.question).to.be.eq(valueLabel);
        });
      });


      it('handle multiRow table', async () => {
        const cols = [
          createColumn({ value: 'yes' }),
          createColumn({ type: ColumnType.Number, value: 50 }),
        ];

        const colsSecondRow = [
          createColumn({ type: ColumnType.Number, value: 150, comment: 'Second row comment' }),
          createColumn({ value: 'no' }),
        ];

        const codes = [undefined, 30];
        const secondTableSecondRow = colsSecondRow.map((col, i) => {
          return { ...col, value: codes[i] }
        })

        const dataProvider: DataProvider = [
          [createTableValidation(cols), [cols]],
          [createTableValidation(colsSecondRow), [colsSecondRow, secondTableSecondRow] ],
        ];

        const transformData = createTransformData(dataProvider);
        const data = await dt.transformToRows(transformData, {
          utrs: transformData.map(v => v.universalTracker),
          preferredTypes: ['sgx_metrics'],
        })

        let nextQuestionIndex = 0;
        transformData.forEach((utrv) => {

          const valueRows = utrv.valueData?.table ?? [];
          const valueValidation = utrv.universalTracker.valueValidation;
          const columns = valueValidation?.table?.columns ?? [];
          valueRows.forEach((row, index) => {
            columns.forEach((column, i) => {
              const valueColumn = row.find(col => col.code === column.code);
              const questionRow = data[nextQuestionIndex+i];
              // First row, first column
              if (index === 0 && i === 0) {
                expect(utrv.note).to.be.eq(questionRow.note)
              }
              expect(column.code).to.be.eq(questionRow.columnCode)
              expect(column.name).to.be.eq(questionRow.columnName)
              expect(valueColumn?.value).to.be.eq((questionRow.values || [])[index])
            })
          })
          nextQuestionIndex+=columns.length;
        });
        data.forEach((utrv) => {
          expect(utrv.typeCode).to.be.eq(altTypeCode);
          expect(utrv.question).to.be.eq(valueLabel);
        });
      });
    });

    describe('simple numeric types with decimal set', async () => {
      const utr = createUtrFromCode(`random-utr-code`, {
        valueType: UtrValueType.Number,
      });

      const testCases = [
        {
          valueValidation: { decimal: undefined },
          value: undefined,
          expectedValue: undefined,
        },
        {
          valueValidation: { decimal: undefined },
          value: 1,
          expectedValue: getNumericCellFormat({ value: 1 }),
        },
        {
          valueValidation: { decimal: 2 },
          value: 1,
          expectedValue: {
            value: 1,
            options: { type: 'n', format: '0.00' },
          },
        },
        {
          valueValidation: { decimal: 2 },
          value: 1.2345,
          expectedValue: getNumericCellFormat({ value: 1.2345 }), // keep original value if no missing decimal to add
        },
        {
          valueValidation: { decimal: 5 },
          value: 1.2345,
          expectedValue: {
            value: 1.2345,
            options: { type: 'n', format: '0.00000' }, // expected: 1.23450
          },
        },
      ];

      testCases.forEach(({ valueValidation, value, expectedValue }) => {
        it('should transform to rows with decimal set', async () => {
          const utrv = createUtrv(new ObjectId(), utr._id, undefined, { value });
          const utrOverridesMap = new Map([
            [utr._id.toString(), { _id: new ObjectId(), universalTrackerId: utr._id, valueValidation }],
          ]);
          const result = await dt.transformToRows([utrv], {
            utrs: [utr],
            utrOverridesMap,
          });

          result.forEach((questionRow) => {
            expect(expectedValue).to.be.deep.eq(questionRow.value);
          });
        });
      });
    });

    describe('NumericValueList type with decimal set', async () => {
      const griValueList = createValueValidation(griValueListTestOne);
      const [valueList1, valueList2, valueList3] = griValueList.valueList?.list || [];

      const utr = createUtrFromCode(`random-utr-code`, {
        valueType: UtrValueType.NumericValueList,
        valueValidation: griValueList,
      });

      const valueData = { data: { [valueList1.code]: 10, [valueList2.code]: '20.00', [valueList3.code]: undefined } };

      const testCases = [
        {
          valueValidation: { decimal: undefined },
          expectedValue: [
            getNumericCellFormat({ value: 10 }),
            getNumericCellFormat({ value: '20.00' }),
            undefined,
          ],
        },
        {
          valueValidation: { decimal: 1 },
          expectedValue: [
            getNumericCellFormat({ value: 10, format: '0.0' }),
            getNumericCellFormat({ value: '20.00' }),
            undefined,
          ],
        },
        {
          valueValidation: { decimal: 3 },
          expectedValue: [
            getNumericCellFormat({ value: 10, format: '0.000' }),
            getNumericCellFormat({ value: '20.00', format: '0.000' }),
            undefined,
          ],
        },
      ];

      testCases.forEach(({ valueValidation, expectedValue }) => {
        it('should transform to rows with decimal set', async () => {
          const utrv = createUtrv(new ObjectId(), utr._id, undefined, { valueData });
          const utrOverridesMap = new Map([[utr._id.toString(), { _id: new ObjectId(), universalTrackerId: utr._id, valueValidation }]]);
          const result = await dt.transformToRows([utrv], {
            utrs: [utr],
            utrOverridesMap,
          });

          result.forEach((questionRow, index) => {
            expect(expectedValue[index]).to.be.deep.eq(questionRow.value);
          });
        });
      });
    });

    describe('Table type with decimal set', async () => {
      const columns = [
        createColumn({ code: 'a', type: ColumnType.Number }),
        createColumn({ code: 'b', type: ColumnType.Percentage }),
        createColumn({ code: 'c', type: ColumnType.Number }),
      ];

      // Results of these values will be transposed in sheet
      const firstRowValues = ['1', '12.345', undefined];
      const secondRowValues = ['2', '0', '8'];
      const partialRowValues = [3, undefined, '3'];
      const halfEmptyRowValues = ['4.67', '4.445', '4.66'];

      // Expected results of all these rows after
      // @TODO these should actually be FormattedCell,
      //  instead to keep the number type, instead of converting to string
      const fullRowExpected = [
        ['1.00', '2.00', '3.00', '4.67'],
        ['12.345', '0.00', undefined, '4.445'],
        [undefined, '8.00', '3.00', '4.66'],
      ];

      const firstRow = columns.map((col, index) => ({...col, value: firstRowValues[index]}))
      const secondRow = columns.map((col, index) => ({...col, value: secondRowValues[index]}))
      const partialRow = columns.map((col, index) => ({...col, value: partialRowValues[index]}))
      const halfEmptyRow = columns.map((col, index) => ({...col, value: halfEmptyRowValues[index]}))

      const testCases = [
        {
          label: 'single-row: no input data',
          customValidation: { decimal: 2 },
          value: [],
          tableValidation: createTableValidation(columns, 1),
          expectedValues: [[undefined], [undefined], [undefined]],
        },
        {
          label: 'single-row: has input data without decimal set',
          customValidation: { decimal: undefined },
          value: [firstRow],
          tableValidation: createTableValidation(columns, 1),
          expectedValues: [['1'], ['12.345'], [undefined]],
        },
        {
          label: 'single-row: has input data with decimal set',
          customValidation: { decimal: 2 },
          value: [firstRow],
          tableValidation: createTableValidation(columns, 1),
          expectedValues: [['1.00'], ['12.345'], [undefined]], // keep '12.345' since decimal = 2 is less than value's decimal
        },
        {
          label: 'multi-row: has input data with decimal set',
          customValidation: { decimal: 2 },
          value: [firstRow, secondRow],
          tableValidation: createTableValidation(columns),
          expectedValues: [
            ['1.00', '2.00'],
            ['12.345', '0.00'],
            [undefined, '8.00'],
          ],
        },
        {
          label: 'multi-row: 4 rows, has input partial data set',
          customValidation: { decimal: 2 },
          value: [firstRow, secondRow, partialRow, halfEmptyRow],
          tableValidation: createTableValidation(columns),
          expectedValues: fullRowExpected,
        },
      ];

      testCases.forEach(({ label, tableValidation, value, customValidation, expectedValues }) => {
        it(`should transform to rows when table is ${label}`, async () => {
          const utr = createUtrFromCode(`random-utr-code`, {
            valueType: UtrValueType.Table,
            valueValidation: tableValidation,
          });

          const utrv = createUtrv(new ObjectId(), utr._id, undefined, { valueData: { table: value } });

          const utrOverridesMap = new Map([
            [
              utr._id.toString(),
              {
                _id: new ObjectId(),
                universalTrackerId: utr._id,
                valueValidation: {
                  table: {
                    columns:
                      tableValidation.table?.columns.map((col) => ({
                        ...col,
                        validation: customValidation,
                      })) ?? [],
                  },
                },
              },
            ],
          ]);
          const result = await dt.transformToRows([utrv], {
            utrs: [utr],
            utrOverridesMap,
          });
          result.forEach((questionRow, index) => {
            expect(expectedValues[index]).to.be.deep.eq(questionRow.values);
          });
        });
      });
    });
  });
});
