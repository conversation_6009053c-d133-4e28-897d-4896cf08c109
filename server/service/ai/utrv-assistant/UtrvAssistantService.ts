import { Logger } from 'winston';
import { wwgLogger } from '../../wwgLogger';
import { UtrvPromptInput } from './types';
import { UtrvAssistantPromptGenerator } from './UtrvAssistantPromptGenerator';
import { AIModelFactory, AIModelType, getAIModelFactory } from '../AIModelFactory';
import { zodResponseFormat } from 'openai/helpers/zod';
import { utrvAssistantResponseDto } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { DEFAULT_MAX_TOKEN } from './constants';
import { AIModel } from '../models/AIModel';

export type AIUtrvSuggestion = {
  predictedAnswer?: string | number | { [key: string]: string | number | string[] };
  questionExplanation: string;
  bestPractice: string[];
  keyInfo: string[];
  suggestedEvidence: {
    primaryDocumentation: string[];
    supportingDocumentation: string[];
  };
  whereToFind: {
    externalSource: string[];
    internalSource: string[];
  };
};

export const fallbackSuggestion: AIUtrvSuggestion = {
  predictedAnswer: '',
  questionExplanation: '',
  bestPractice: [],
  keyInfo: [],
  suggestedEvidence: {
    primaryDocumentation: [],
    supportingDocumentation: [],
  },
  whereToFind: {
    externalSource: [],
    internalSource: [],
  },
};

/**
 * @TODO: should be removed and merged with AiService
 */
export class UtrvAssistantService {
  constructor(private logger: Logger, private aiModelFactory: AIModelFactory) {}

  private readonly defaultModel = AIModelType.ChatGPT;

  public async getResponse(input: UtrvPromptInput): Promise<AIUtrvSuggestion> {
    const prompt = new UtrvAssistantPromptGenerator(input).generatePrompt();
    const responseFormat = zodResponseFormat(utrvAssistantResponseDto, 'utrvAssistantExtraction');
    // @TODO: [GU-6240] model should come from the input...
    const response = await this.aiModelFactory.getAiModel(this.defaultModel).parseCompletion<AIUtrvSuggestion>(
      [prompt],
      DEFAULT_MAX_TOKEN,
      responseFormat
    );
    return response.content || fallbackSuggestion;
  }

  public getModelVersion() {
    return this.aiModelFactory.getAiModel(this.defaultModel).getModelVersion();
  }
}

let instance: UtrvAssistantService | undefined;
export const getUtrvAssistantManager = () => {
  if (!instance) {
    instance = new UtrvAssistantService(wwgLogger, getAIModelFactory());
  }
  return instance;
};
