/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import BackgroundJob, { CreateJob, JobType, TaskStatus, TaskType } from '../../../models/backgroundJob';
import ContextError from '../../../error/ContextError';
import { ProcessedAnswerResult, CreatedJob, TaskAIAutoAnswerSetup, WorkflowCreate } from './types';
import { ObjectId } from 'bson';
import { getCurrentDateStr } from '../../../util/date';
import { createLogEntry } from '../../../service/jobs';
import {
  BackgroundJobService,
  getBackgroundJobService,
} from '../../../service/background-process/BackgroundJobService';
import { LoggerInterface, wwgLogger } from '../../../service/wwgLogger';
import { generatedUUID } from '../../../service/crypto/token';
import Survey, { SurveyWithInitiative } from '../../../models/survey';
import { ActionList } from '../../../service/utr/constants';
import UniversalTrackerValue, { UniversalTrackerValueModelExtended } from '../../../models/universalTrackerValue';
import { getReportName } from '../../../util/survey';
import { InitiativePlain } from '../../../models/initiative';
import { getUtrvAssistantInputManager, UtrvAssistantInputManager } from '../utrv-assistant/UtrvAssistantInputManager';
import {
  AIUtrvSuggestion,
  getUtrvAssistantManager,
  UtrvAssistantService,
} from '../utrv-assistant/UtrvAssistantService';
import { UtrValueType } from '../../../models/public/universalTrackerType';
import UniversalTrackerActionManager from '../../../service/utr/UniversalTrackerActionManager';
import UniversalTracker, { UniversalTrackerValueListPlain } from '../../../models/universalTracker';
import { universalTrackerPlainFields } from '../../../repository/projections';
import { getPredictedDataResolver, PredictedDataResolver } from '../PredictedDataResolver';
import { ValueDataSourceType } from '../../../models/public/universalTrackerValueType';
import { AiService, getAiService } from '../service';

export class SurveyAutoAnswerService {
  constructor(
    private logger: LoggerInterface,
    private bgJobService: BackgroundJobService,
    private assistantInputManager: UtrvAssistantInputManager,
    private assistantManager: UtrvAssistantService,
    private dataResolver: PredictedDataResolver,
    private aiService: AiService
  ) {}

  private async getSetupTask(survey: SurveyWithInitiative): Promise<TaskAIAutoAnswerSetup> {
    const createdUtrvs = await UniversalTrackerValue.find(
      { _id: { $in: survey.visibleUtrvs }, status: ActionList.Created },
      { _id: 1 }
    )
      .lean<{ _id: ObjectId }[]>()
      .exec();

    return {
      id: generatedUUID(),
      name: 'Set up utrvs for sequential automatic answering',
      type: TaskType.AIAutoAnswerSetup,
      status: TaskStatus.Pending,
      data: {
        utrvIds: createdUtrvs.map(({ _id }) => _id),
        surveyId: survey._id,
      },
    };
  }

  /**
   * Workflow contains 3 main tasks
   *  1. Setup created utrvs of the survey, each utrv is processed within a task
   *  2. Process through each utrv, re-use AI assistant, and save suggested data
   *  3. Complete with a notification
   */
  public async createJob(workflow: WorkflowCreate & { idempotencyKey: string }): Promise<CreatedJob> {
    const { initiativeId, surveyId, userId, idempotencyKey } = workflow;

    const survey = await Survey.findById(surveyId).populate('initiative').orFail().lean<SurveyWithInitiative>().exec();
    const setupTask = await this.getSetupTask(survey);

    const createData: CreateJob = {
      _id: new ObjectId(),
      idempotencyKey,
      type: JobType.AIAutoAnswerSurvey,
      name: `${getCurrentDateStr()} AI automatic answer survey: ${getReportName(survey)}`,
      tasks: [setupTask],
      logs: [createLogEntry('Starting the automatic answer workflow')],
      userId,
      initiativeId,
    };
    const job = await BackgroundJob.create(createData);

    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          initiativeId: job.initiativeId,
          cause: e,
        })
      );
    });

    return {
      jobId: job._id.toString(),
      status: job.status,
    };
  }

  private async applyPredictedAnswer({
    utrv,
    predictedAnswer,
    userId,
    jobId,
  }: {
    utrv: UniversalTrackerValueModelExtended;
    predictedAnswer: AIUtrvSuggestion['predictedAnswer'];
    userId: ObjectId;
    jobId: ObjectId;
  }): Promise<ProcessedAnswerResult> {
    if (!predictedAnswer) {
      return {
        isSuccess: false,
        errorMessage: 'Not found any predicted answer from AI',
      };
    }

    const utr = await UniversalTracker.findById(utrv.universalTracker._id, universalTrackerPlainFields)
      .populate('valueListOptions')
      .populate('tableColumnValueListOptions')
      .lean<UniversalTrackerValueListPlain>()
      .exec();

    if (!utr) {
      return {
        isSuccess: false,
        errorMessage: `Not found a UTR of this utrv: ${utrv._id.toString()}`,
      };
    }

    if ([UtrValueType.Date].includes(utr.valueType)) {
      return {
        isSuccess: false,
        errorMessage: 'Not yet support this metric type',
      };
    }

    const { value, valueData } = this.dataResolver.getValueDataPredictedAnswer({ utr, predictedAnswer });

    if (!value && !valueData) {
      return {
        isSuccess: false,
        errorMessage: 'Not have actual data to update',
      };
    }

    // hydrate valueData for additional AI information
    if (valueData) {
      valueData.source = {
        type: ValueDataSourceType.AIAutoAnswer,
        data: {
          model: this.assistantManager.getModelVersion(),
          backgroundJobId: jobId,
        },
      };
    }

    // predicted answer use default utr props (no input, so we can use the same)
    const noteResponse = await this.aiService.getFurtherNotesDraft(
      utrv,
      {
        value,
        unit: utr.unit,
        numberScale: utr.numberScale,
        // Table will figure out  from utrv.universalTracker
        valueData,
      },
      userId,
    );
    const note = noteResponse.content;

    UniversalTrackerActionManager.hydrateUpdate({
      utrv,
      userId,
      value,
      valueData,
      note,
    });

    await utrv.save();

    return {
      isSuccess: true,
      value,
      valueData,
      note,
    };
  }

  public async processAnswerUtrv({
    initiative,
    utrvId,
    userId,
    jobId,
  }: {
    initiative: InitiativePlain;
    utrvId: ObjectId;
    userId: ObjectId;
    jobId: ObjectId;
  }): Promise<ProcessedAnswerResult> {
    // Re-check the snapshot for only created utrv
    const utrv = (await UniversalTrackerValue.findOne({
      _id: utrvId,
      status: ActionList.Created,
      deletedDate: { $exists: false },
    })
      .populate('universalTracker')
      .exec()) as UniversalTrackerValueModelExtended;

    if (!utrv) {
      return {
        isSuccess: false,
        errorMessage: `The utrv was updated and no longer needs automatic answer: ${utrvId}`,
      };
    }

    const utrvAssistantInput = await this.assistantInputManager.prepareUtrvPromptInput({ initiative, utrv });
    const aiResponse = await this.assistantManager.getResponse(utrvAssistantInput);

    return this.applyPredictedAnswer({ utrv, predictedAnswer: aiResponse.predictedAnswer, userId, jobId });
  }
}

let instance: SurveyAutoAnswerService;
export const getSurveyAutoAnswerService = () => {
  if (!instance) {
    instance = new SurveyAutoAnswerService(
      wwgLogger,
      getBackgroundJobService(),
      getUtrvAssistantInputManager(),
      getUtrvAssistantManager(),
      getPredictedDataResolver(),
      getAiService()
    );
  }
  return instance;
};
