import { ObjectId } from 'bson';
import { CalculationGroup, CalculationGroupPlain, CalculationGroupValueType } from '../models/calculationGroup';
import UniversalTracker from '../models/universalTracker';
import { UniversalTrackerConnectionPlain, UniversalTrackerConnection } from '../models/universalTrackerConnection';
import { CalculationType } from '../models/calculationGroup';
import { NumberScale, SupportedMeasureUnits } from "../service/units/unitTypes";

export interface ExpandedConnection extends Omit<UniversalTrackerConnectionPlain, 'calculations'> {
  calculationGroups: CalculationGroupPlain[];
}

export class UniversalTrackerConnectionRepository {
  constructor(
    private model: typeof UniversalTrackerConnection,
    private utrModel: typeof UniversalTracker,
    private calculationGroupModel: typeof CalculationGroup
  ) {}

  async getConnections(utrId: ObjectId): Promise<ExpandedConnection[]> {
    const { code: utrCode } = await this.utrModel.findById(utrId, { code: 1 }).orFail().lean().exec();

    const utrStaticConnections = await this.getUtrConnections(utrCode);
    const utrConnections = await this.model.find({ utrCode }).lean<UniversalTrackerConnectionPlain[]>().exec();
    if (!utrConnections.length) {
      return utrStaticConnections;
    }

    const calculationGroupIdSet = new Set<string>();
    utrConnections.forEach((connection) =>
      connection.calculations.forEach(({ calculationGroupId }) => {
        calculationGroupIdSet.add(calculationGroupId.toString());
      })
    );

    const calculationGroupIds = Array.from(calculationGroupIdSet).map((id) => new ObjectId(id));
    const calculationGroups = await this.calculationGroupModel
      .find({ _id: { $in: calculationGroupIds } })
      .lean()
      .exec();

    const extendedConnections = utrConnections.map((connection) => {
      const { calculations, ...rest } = connection;
      return {
        ...rest,
        calculationGroups: calculationGroups.filter((group) =>
          calculations.some(({ calculationGroupId }) => calculationGroupId.equals(group._id))
        ),
      };
    });

    return [...utrStaticConnections, ...extendedConnections];
  }

  private async getUtrConnections(utrCode: string): Promise<ExpandedConnection[]> {
    const staticConnections: ExpandedConnection[] = this.getStaticConnections();
    // Look up static definitions for the utr
    return staticConnections.filter((connection) => connection.utrCode === utrCode);
  }


  private getStaticConnections(): ExpandedConnection[] {
    // Create new object to ensure we don't mutate the original object
    return [
      {
        _id: new ObjectId('665800000000000000000000'),
        code: 'static-connection-greenly-one',
        // Map utrCode to a static connection
        utrCode: 'gri/2020/305-1/a',
        calculationGroups: [
          {
            _id: new ObjectId('665800000000000000000001'),
            code: 'greenly-total-scope-1',
            name: 'Greenly Total Scope 1',
            valueType: CalculationGroupValueType.Numeric,
            unitType: SupportedMeasureUnits.mass,
            numberScale: NumberScale.Single,
            calculations: [
              {
                _id: new ObjectId('665800000000000000000002'),
                name: 'Greenly Total Scope 1',
                type: CalculationType.Direct,
                direct: 'a',
                variables: {
                  a: {
                    code: 'greenly-total-scope-1',
                    valueListCode: 'scope_1_total',
                    integrationCode: 'greenly'
                  },
                },
              },
            ],
            created: new Date('2025-06-02T15:00:00.000Z'),
            oktaId: '',
          },
        ],
      },
      // Add gri/2020/305-3/a Gross Other Indirect (Scope 3) Emissions
      {
        _id: new ObjectId('665800000000000000000003'),
        code: 'static-connection-greenly-three',
        utrCode: 'gri/2020/305-3/a',
        calculationGroups: [
          {
            _id: new ObjectId('665800000000000000000004'),
            code: 'greenly-total-scope-3',
            name: 'Greenly Total Scope 3',
            valueType: CalculationGroupValueType.Numeric,
            unitType: SupportedMeasureUnits.mass,
            numberScale: NumberScale.Single,
            calculations: [
              {
                _id: new ObjectId('665800000000000000000005'),
                name: 'Greenly Total Scope 3',
                type: CalculationType.Direct,
                direct: 'a',
                variables: {
                  a: {
                    code: 'greenly-total-scope-3',
                    valueListCode: 'scope_3_total',
                    integrationCode: 'greenly'
                  },
                },
              },
            ],
            created: new Date('2025-06-02T15:00:00.000Z'),
            oktaId: '',
          },
        ],
      },
    ] as ExpandedConnection[];
  }
}

let instance: UniversalTrackerConnectionRepository;

export const getUniversalTrackerConnectionRepository = () => {
  if (!instance) {
    instance = new UniversalTrackerConnectionRepository(UniversalTrackerConnection, UniversalTracker, CalculationGroup);
  }
  return instance;
};
