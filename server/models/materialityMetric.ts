/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { Model, model, Schema, HydratedDocument } from 'mongoose';
import { ObjectId } from 'bson';
import { UniversalTrackerPlain } from './universalTracker';
import ValueList from './valueList';
import { MaterialityAssessmentScope } from '../service/materiality-assessment/types';
import { ValueListBase } from './public/valueList';

export enum MaterialityAssessmentType {
  Financial = 'financial',
  Impact = 'non-financial',
}

type MaterialityMetricTag = MaterialityAssessmentScope | MaterialityAssessmentType;

export interface MaterialityMetricBase {
  code: string;
  utrCode: string;
  type: MaterialityAssessmentType;
  valueListCode: string;
  options: {
    optionCode: string;
    scores: {
      materialityCode: string;
      score: number;
    }[];
  }[];
  tags?: MaterialityMetricTag[];
}

export interface MaterialityMetric<T = ObjectId> extends MaterialityMetricBase {
  _id: T;
  created: Date;
}

type MaterialTopicDocument<T = ObjectId> = HydratedDocument<MaterialityMetric<T>>;

export type MaterialityMetricPlain<T = string> = MaterialityMetric<T>;
export type MaterialityMetricWithValueListPlain<T = string> = MaterialityMetricPlain<T> & { valueList?: typeof ValueList };
export type MaterialityMetricWithUtrAndValueListPlain<T = string> = MaterialityMetricWithValueListPlain<T> & {
  utr?: UniversalTrackerPlain<T>;
  valueList?: ValueListBase;
};
export type MaterialityMetricWithUtrPlain<T = string> = MaterialityMetricPlain<T> & {
  utr?: UniversalTrackerPlain<T>;
};

const codeRegex = /^[a-z0-9\/\-.]+$/;
const codeRegexErrorMsg = 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/)';

const G17ecoCodeSchema = {
  type: Schema.Types.String,
  trim: true,
  lowercase: true,
  required: true,
  validate: [codeRegex, codeRegexErrorMsg] as [RegExp, string],
};

const G17ecoCodeSchemaUnique = {
  ...G17ecoCodeSchema,
  unique: true,
};

const MaterialityMetricOptionScoreSchema = new Schema(
  {
    materialityCode: Schema.Types.String,
    score: { type: Schema.Types.Number, required: true },
  },
  { _id: false }
);

const MaterialityMetricOptionSchema = new Schema(
  {
    optionCode: G17ecoCodeSchema,
    scores: [MaterialityMetricOptionScoreSchema],
  },
  { _id: false }
);

export const MaterialityMetricSchema = new Schema<MaterialTopicDocument>(
  {
    created: { type: Schema.Types.Date, default: Date.now },
    code: G17ecoCodeSchemaUnique,
    utrCode: Schema.Types.String,
    valueListCode: Schema.Types.String,
    type: {
      type: Schema.Types.String,
      required: true,
      enum: [MaterialityAssessmentType.Financial, MaterialityAssessmentType.Impact],
      default: MaterialityAssessmentType.Financial,
    },
    options: [MaterialityMetricOptionSchema],
    tags: {
      type: [Schema.Types.String],
      trim: true,
      lowercase: true,
      required: false,
      unique: false,
      validate: {
        validator: (v: string[]) => v.every((item) => codeRegex.test(item)),
        message: codeRegexErrorMsg,
      },
    },
  },
  {
    collection: 'materiality-metrics',
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

MaterialityMetricSchema.virtual('utr', {
  ref: 'UniversalTracker',
  localField: 'utrCode',
  foreignField: 'code',
  justOne: true,
  as: 'utr',
});

MaterialityMetricSchema.virtual('valueList', {
  ref: 'ValueListModel',
  localField: 'valueListCode',
  foreignField: 'code',
  justOne: true,
  as: 'valueList',
});

MaterialityMetricSchema.index({ utrCode: 1, type: 1 }, { unique: false });
MaterialityMetricSchema.index({ code: 1 }, { unique: true });
MaterialityMetricSchema.index({ tags: 1, type: 1 }, { unique: false });

const MaterialityMetric: Model<MaterialTopicDocument> = model('MaterialityMetric', MaterialityMetricSchema);
export default MaterialityMetric;
